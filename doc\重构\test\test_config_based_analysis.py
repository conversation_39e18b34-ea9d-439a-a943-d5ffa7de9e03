#!/usr/bin/env python3
"""
基于配置文件的统一意图分析测试

此测试基于 f:\工作\autopilotai\config\default.yaml 配置文件，
使用实际的Agent进行测试，验证统一分析功能的开关控制和性能表现。
"""

import asyncio
import json
import time
import sys
import os
import yaml
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.append(str(project_root))

from src.core.config import get_settings
from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
from src.core.llm_manager import LLMManager


class ConfigBasedAnalysisTest:
    """基于配置文件的分析测试类"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_path = self.project_root / "config" / "default.yaml"
        self.settings = None
        self.agent = None
        self.test_results = []
        
    async def setup(self):
        """测试环境初始化"""
        print("🔧 初始化基于配置文件的测试环境...")
        print(f"📁 项目根目录: {self.project_root}")
        print(f"⚙️ 配置文件路径: {self.config_path}")
        
        # 检查配置文件是否存在
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        # 加载配置
        self.settings = get_settings()
        print(f"✅ 配置加载成功")
        print(f"  - 推理模型: {self.settings.reasoning_llm.model}")
        print(f"  - 基础模型: {self.settings.basic_llm.model}")
        
        # 初始化Agent
        self.agent = TravelPlannerAgentLangGraph()
        print(f"✅ Agent初始化成功")
        
    def load_config_file(self) -> Dict[str, Any]:
        """加载配置文件内容"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
            
    def update_config_file(self, config_data: Dict[str, Any]):
        """更新配置文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
    async def test_config_switch_functionality(self):
        """测试配置开关功能"""
        print("\n" + "=" * 60)
        print("🔄 测试1: 配置开关功能测试")
        print("=" * 60)
        
        # 读取当前配置
        original_config = self.load_config_file()
        print(f"📋 当前配置状态:")
        
        # 检查是否存在统一分析配置项
        unified_analysis_config = original_config.get('unified_analysis', {})
        if unified_analysis_config:
            print(f"  ✅ 发现统一分析配置:")
            print(f"    - 启用状态: {unified_analysis_config.get('enabled', False)}")
            print(f"    - 性能监控: {unified_analysis_config.get('performance_monitoring', False)}")
            print(f"    - 自动回退: {unified_analysis_config.get('auto_fallback', True)}")
        else:
            print(f"  ⚠️ 未发现统一分析配置，将创建默认配置")
            
            # 添加默认统一分析配置
            unified_analysis_config = {
                'enabled': False,
                'performance_monitoring': True,
                'auto_fallback': True,
                'max_response_time_ms': 10000,
                'max_token_usage': 2000
            }
            original_config['unified_analysis'] = unified_analysis_config
            self.update_config_file(original_config)
            print(f"  ✅ 已添加默认统一分析配置")
            
        # 测试配置切换
        test_scenarios = [
            {'enabled': False, 'description': '传统模式（4次LLM调用）'},
            {'enabled': True, 'description': '统一模式（1次LLM调用）'}
        ]
        
        test_query = "我想开车去北京和上海玩5天，主要想看现代建筑和品尝美食，预算5000元"
        
        for scenario in test_scenarios:
            print(f"\n🧪 测试场景: {scenario['description']}")
            
            # 更新配置
            config_data = self.load_config_file()
            config_data['unified_analysis']['enabled'] = scenario['enabled']
            self.update_config_file(config_data)
            
            # 重新加载配置和Agent
            self.settings = get_settings()
            self.agent = TravelPlannerAgentLangGraph()
            
            print(f"  ⚙️ 配置已切换为: {'统一模式' if scenario['enabled'] else '传统模式'}")
            
            try:
                start_time = time.time()
                
                # 使用实际Agent进行测试
                user_profile = {
                    "user_id": "test_user",
                    "preferences": {
                        "budget_range": "3000-8000",
                        "travel_style": "文化探索",
                        "accommodation_preference": "舒适型酒店"
                    }
                }
                
                # 调用Agent的规划功能
                result = await self.agent.plan_travel(
                    query=test_query,
                    user_profile=user_profile
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                print(f"  ✅ 规划完成，耗时: {response_time:.2f}秒")
                
                # 分析结果结构
                if isinstance(result, dict):
                    print(f"  📊 结果分析:")
                    print(f"    - 结果类型: {type(result).__name__}")
                    print(f"    - 主要字段: {list(result.keys())[:5]}")
                    
                    # 检查核心分析结果
                    if 'core_intent' in result:
                        core_intent = result['core_intent']
                        print(f"    - 识别目的地: {core_intent.get('destinations', [])}")
                        print(f"    - 识别天数: {core_intent.get('days', 0)}")
                        
                    if 'itinerary' in result:
                        itinerary = result['itinerary']
                        if isinstance(itinerary, list):
                            print(f"    - 行程天数: {len(itinerary)}天")
                        
                # 记录测试结果
                self.test_results.append({
                    'scenario': scenario['description'],
                    'enabled': scenario['enabled'],
                    'success': True,
                    'response_time': response_time,
                    'result_structure': list(result.keys()) if isinstance(result, dict) else str(type(result))
                })
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                self.test_results.append({
                    'scenario': scenario['description'],
                    'enabled': scenario['enabled'],
                    'success': False,
                    'error': str(e)
                })
                
        # 恢复原始配置
        self.update_config_file(original_config)
        print(f"\n🔄 已恢复原始配置")
        
    async def test_performance_monitoring_config(self):
        """测试性能监控配置"""
        print("\n" + "=" * 60)
        print("📊 测试2: 性能监控配置测试")
        print("=" * 60)
        
        # 读取配置
        config_data = self.load_config_file()
        
        # 设置性能监控参数
        monitoring_config = {
            'enabled': True,
            'performance_monitoring': True,
            'max_response_time_ms': 5000,  # 5秒超时
            'max_token_usage': 1500,       # Token限制
            'auto_fallback': True
        }
        
        config_data['unified_analysis'] = monitoring_config
        self.update_config_file(config_data)
        
        print(f"⚙️ 性能监控配置:")
        print(f"  - 最大响应时间: {monitoring_config['max_response_time_ms']}ms")
        print(f"  - 最大Token使用: {monitoring_config['max_token_usage']}")
        print(f"  - 自动回退: {monitoring_config['auto_fallback']}")
        
        # 重新加载配置
        self.settings = get_settings()
        self.agent = TravelPlannerAgentLangGraph()
        
        # 测试不同复杂度的查询
        test_queries = [
            {
                "name": "简单查询",
                "query": "我想去北京玩3天",
                "expected_within_limits": True
            },
            {
                "name": "复杂查询", 
                "query": "我想开电动车从北京出发，途经天津、济南、南京、苏州、杭州、上海，最后到达深圳，总共玩15天，每个城市都要体验当地特色美食、历史文化景点、现代建筑、购物中心、夜生活，预算控制在每天500元以内，住宿要求四星级以上酒店，交通工具除了自驾还要体验高铁、飞机，希望能安排一些户外活动和文化体验项目",
                "expected_within_limits": False
            }
        ]
        
        for test_query in test_queries:
            print(f"\n🧪 测试: {test_query['name']}")
            print(f"   查询长度: {len(test_query['query'])} 字符")
            
            try:
                start_time = time.time()
                
                result = await self.agent.plan_travel(
                    query=test_query['query'],
                    user_profile={"user_id": "test_user"}
                )
                
                end_time = time.time()
                response_time_ms = (end_time - start_time) * 1000
                
                print(f"   ✅ 响应时间: {response_time_ms:.0f}ms")
                
                # 检查是否超出限制
                within_time_limit = response_time_ms <= monitoring_config['max_response_time_ms']
                print(f"   {'✅' if within_time_limit else '⚠️'} 时间限制: {'通过' if within_time_limit else '超出'}")
                
                # 模拟Token使用检查（实际应该从LLM响应中获取）
                estimated_tokens = len(test_query['query']) * 2  # 简单估算
                within_token_limit = estimated_tokens <= monitoring_config['max_token_usage']
                print(f"   {'✅' if within_token_limit else '⚠️'} Token限制: {'通过' if within_token_limit else '超出'} (估算: {estimated_tokens})")
                
                if test_query['expected_within_limits']:
                    if within_time_limit and within_token_limit:
                        print(f"   ✅ 符合预期：简单查询在限制范围内")
                    else:
                        print(f"   ⚠️ 不符合预期：简单查询超出限制")
                else:
                    if not within_time_limit or not within_token_limit:
                        print(f"   ✅ 符合预期：复杂查询触发限制")
                    else:
                        print(f"   ⚠️ 不符合预期：复杂查询未触发限制")
                        
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
                
    async def test_fallback_mechanism(self):
        """测试自动回退机制"""
        print("\n" + "=" * 60)
        print("🔄 测试3: 自动回退机制测试")
        print("=" * 60)
        
        # 配置极低的限制来触发回退
        config_data = self.load_config_file()
        fallback_config = {
            'enabled': True,
            'performance_monitoring': True,
            'max_response_time_ms': 100,   # 极低的时间限制
            'max_token_usage': 50,         # 极低的Token限制
            'auto_fallback': True
        }
        
        config_data['unified_analysis'] = fallback_config
        self.update_config_file(config_data)
        
        print(f"⚙️ 回退测试配置（极限参数）:")
        print(f"  - 最大响应时间: {fallback_config['max_response_time_ms']}ms")
        print(f"  - 最大Token使用: {fallback_config['max_token_usage']}")
        
        # 重新加载配置
        self.settings = get_settings()
        self.agent = TravelPlannerAgentLangGraph()
        
        test_query = "我想去北京和上海玩5天，请详细规划行程"
        
        try:
            print(f"\n🧪 执行回退测试查询...")
            print(f"   查询: {test_query}")
            
            start_time = time.time()
            
            result = await self.agent.plan_travel(
                query=test_query,
                user_profile={"user_id": "test_user"}
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"   ✅ 查询完成，耗时: {response_time:.2f}秒")
            print(f"   📊 结果类型: {type(result).__name__}")
            
            # 分析是否触发了回退机制
            if response_time * 1000 > fallback_config['max_response_time_ms']:
                print(f"   🔄 可能触发了自动回退机制（响应时间超限）")
            else:
                print(f"   ⚠️ 未触发回退机制，可能需要调整测试参数")
                
        except Exception as e:
            print(f"   ❌ 回退测试异常: {e}")
            
    async def test_real_agent_integration(self):
        """测试真实Agent集成"""
        print("\n" + "=" * 60)
        print("🤖 测试4: 真实Agent集成测试")
        print("=" * 60)
        
        # 恢复正常配置
        config_data = self.load_config_file()
        normal_config = {
            'enabled': True,
            'performance_monitoring': True,
            'max_response_time_ms': 30000,  # 30秒
            'max_token_usage': 3000,        # 3000 tokens
            'auto_fallback': True
        }
        
        config_data['unified_analysis'] = normal_config
        self.update_config_file(config_data)
        
        # 重新加载配置
        self.settings = get_settings()
        self.agent = TravelPlannerAgentLangGraph()
        
        print(f"⚙️ 使用正常配置进行Agent集成测试")
        
        # 真实场景测试用例
        real_scenarios = [
            {
                "name": "家庭旅行",
                "query": "我们一家四口想去三亚度假5天，有两个小孩，希望有适合儿童的活动",
                "user_profile": {
                    "user_id": "family_user",
                    "preferences": {
                        "travel_style": "家庭度假",
                        "budget_range": "8000-15000",
                        "special_needs": "儿童友好"
                    }
                }
            },
            {
                "name": "商务出行",
                "query": "我需要去上海出差3天，希望安排一些商务会议和简单的观光",
                "user_profile": {
                    "user_id": "business_user",
                    "preferences": {
                        "travel_style": "商务出行",
                        "budget_range": "5000-10000",
                        "accommodation_preference": "五星级酒店"
                    }
                }
            },
            {
                "name": "自驾游",
                "query": "我想开车从北京到西安，沿途游览历史文化景点，计划7天",
                "user_profile": {
                    "user_id": "driving_user",
                    "preferences": {
                        "travel_style": "自驾探索",
                        "budget_range": "6000-12000",
                        "interests": ["历史文化", "自然风光"]
                    }
                }
            }
        ]
        
        for scenario in real_scenarios:
            print(f"\n🎭 场景测试: {scenario['name']}")
            print(f"   查询: {scenario['query']}")
            
            try:
                start_time = time.time()
                
                result = await self.agent.plan_travel(
                    query=scenario['query'],
                    user_profile=scenario['user_profile']
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                print(f"   ✅ 规划完成，耗时: {response_time:.2f}秒")
                
                # 详细分析结果
                if isinstance(result, dict):
                    print(f"   📋 结果分析:")
                    
                    # 核心意图分析
                    if 'core_intent' in result:
                        intent = result['core_intent']
                        print(f"     🎯 目的地: {intent.get('destinations', [])}")
                        print(f"     📅 天数: {intent.get('days', 0)}")
                        print(f"     🏷️ 主题: {intent.get('travel_theme', 'N/A')}")
                        
                    # 行程安排
                    if 'itinerary' in result:
                        itinerary = result['itinerary']
                        if isinstance(itinerary, list):
                            print(f"     📝 行程: {len(itinerary)}天详细安排")
                            for i, day in enumerate(itinerary[:2], 1):  # 只显示前2天
                                if isinstance(day, dict):
                                    activities = day.get('activities', [])
                                    print(f"       第{i}天: {len(activities)}个活动")
                                    
                    # 多城市策略
                    if 'multi_city_strategy' in result:
                        strategy = result['multi_city_strategy']
                        print(f"     🗺️ 策略: {strategy.get('strategy_type', 'N/A')}")
                        
                    # 自驾情境
                    if 'driving_context' in result:
                        driving = result['driving_context']
                        print(f"     🚗 自驾: {driving.get('is_driving', False)}")
                        
                print(f"   ✅ {scenario['name']}场景测试通过")
                
            except Exception as e:
                print(f"   ❌ {scenario['name']}场景测试失败: {e}")
                
    def generate_config_test_report(self):
        """生成配置测试报告"""
        print("\n" + "=" * 60)
        print("📋 配置测试报告")
        print("=" * 60)
        
        print(f"\n📁 测试环境:")
        print(f"   项目路径: {self.project_root}")
        print(f"   配置文件: {self.config_path}")
        print(f"   配置状态: {'✅ 存在' if self.config_path.exists() else '❌ 缺失'}")
        
        if self.test_results:
            successful_tests = [r for r in self.test_results if r.get('success', False)]
            total_tests = len(self.test_results)
            success_rate = len(successful_tests) / total_tests * 100
            
            print(f"\n📊 测试统计:")
            print(f"   总测试数: {total_tests}")
            print(f"   成功数: {len(successful_tests)}")
            print(f"   成功率: {success_rate:.1f}%")
            
            print(f"\n📝 详细结果:")
            for result in self.test_results:
                status = "✅" if result.get('success') else "❌"
                print(f"   {status} {result.get('scenario', 'Unknown')}")
                if result.get('success'):
                    if 'response_time' in result:
                        print(f"      ⏱️ 响应时间: {result['response_time']:.2f}秒")
                else:
                    print(f"      ❌ 错误: {result.get('error', 'Unknown error')}")
                    
        print(f"\n💡 配置建议:")
        print(f"   ✅ 确保config/default.yaml包含unified_analysis配置项")
        print(f"   ⚙️ 建议设置合理的性能监控阈值")
        print(f"   🔄 启用自动回退机制以保证系统稳定性")
        print(f"   📊 在生产环境中持续监控性能指标")
        print(f"   🧪 定期进行A/B测试验证优化效果")
        
    async def run_all_tests(self):
        """运行所有配置测试"""
        print("🚀 AutoPilot AI - 基于配置文件的统一意图分析测试")
        print("=" * 70)
        print("基于 config/default.yaml 配置和真实Agent的测试验证")
        print("=" * 70)
        
        try:
            await self.setup()
            await self.test_config_switch_functionality()
            await self.test_performance_monitoring_config()
            await self.test_fallback_mechanism()
            await self.test_real_agent_integration()
            self.generate_config_test_report()
            
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理资源
            if hasattr(self, 'agent') and self.agent:
                try:
                    await self.agent.cleanup()
                except:
                    pass
                    

async def main():
    """主函数"""
    test_runner = ConfigBasedAnalysisTest()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())