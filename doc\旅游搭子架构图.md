# 旅游搭子系统架构图

## 系统概述

旅游搭子系统是基于AutoPilot AI架构的智能旅行规划系统，采用多Agent协同、分层记忆体系和实时状态管理的设计理念。系统通过双链路设计支持简单查询和复杂规划两种场景，并具备持续学习和个性化推荐能力。

## 核心架构图

```
                                    旅游搭子系统架构图
                                   (AutoPilot AI Based)

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   用户交互层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│  │   车载终端   │    │   移动APP   │    │   Web界面   │    │   语音助手   │          │
│  │  Car Display │    │ Mobile App  │    │ Web Portal │    │Voice Assistant│          │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘          │
│           │                 │                 │                 │                   │
│           └─────────────────┼─────────────────┼─────────────────┘                   │
│                             │                 │                                     │
└─────────────────────────────┼─────────────────┼─────────────────────────────────────┘
                              │                 │
┌─────────────────────────────┼─────────────────┼─────────────────────────────────────┐
│                           API网关/中枢系统                                           │
├─────────────────────────────┼─────────────────┼─────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        输入适配器 (Input Adapter)                           │   │
│  │                    语音转文本 | 格式化 | 意图预处理                           │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      任务管理器 (Task Manager)                               │   │
│  │                  生成task_id | 路由分发 | 状态跟踪                           │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      SSE流式输出 (SSE Streaming)                             │   │
│  │                   实时进度推送 | 事件通知 | 结果分发                          │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                 上下文获取层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    上下文获取协调器 (Context Manager)                        │   │
│  │                        AutoGen GroupChatManager                             │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 位置Agent   │  │ 时间Agent   │  │ 车辆Agent   │  │ 用户Agent   │              │
│  │Location     │  │Time Context │  │Vehicle      │  │User Profile │              │
│  │Context      │  │Agent        │  │Status       │  │Agent        │              │
│  │Agent        │  │             │  │Agent        │  │             │              │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘              │
│         │                │                │                │                      │
│         └────────────────┼────────────────┼────────────────┘                      │
│                          │                │                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    上下文聚合器 (Context Aggregator)                         │   │
│  │                      更新AgentState到Redis                                  │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                               核心决策与路由层                                        │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                   主规划路由器 (Main Planner Router)                         │   │
│  │                        AutoGen AssistantAgent                               │   │
│  │                    ┌─────────────────────────────────┐                      │   │
│  │                    │      记忆决策模块                │                      │   │
│  │                    │   L0: 当前请求信息               │                      │   │
│  │                    │   L1: 短期记忆(Redis)           │                      │   │
│  │                    │   L2: 长期记忆(需要时调用)       │                      │   │
│  │                    └─────────────────────────────────┘                      │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│                        ┌───────────────┼───────────────┐                           │
│                        │               │               │                           │
│                   简单请求         复杂请求        陪伴请求                          │
│                        │               │               │                           │
└────────────────────────┼───────────────┼───────────────┼───────────────────────────┘
                         │               │               │
┌────────────────────────┼───────────────┼───────────────┼───────────────────────────┐
│                      链路一：简单任务处理                │                           │
├────────────────────────┼───────────────┼───────────────┼───────────────────────────┤
│  ┌─────────────────────────────────────┐               │                           │
│  │        简单任务Agent                 │               │                           │
│  │    (Simple Task Agent)              │               │                           │
│  │      AutoGen AssistantAgent         │               │                           │
│  │                                     │               │                           │
│  │  • 基础信息查询 (天气、路线)         │               │                           │
│  │  • 单点POI搜索                      │               │                           │
│  │  • 简单路线规划                     │               │                           │
│  │  • 快速问答                         │               │                           │
│  └─────────────────────────────────────┘               │                           │
│                         │                               │                           │
│  ┌─────────────────────────────────────┐               │                           │
│  │         工具执行器                   │               │                           │
│  │      (Tool Executor)                │               │                           │
│  │                                     │               │                           │
│  │  ┌─────────────────────────────────┐ │               │                           │
│  │  │        高德地图工具集            │ │               │                           │
│  │  │     (Amap MCP Tools)           │ │               │                           │
│  │  │                               │ │               │                           │
│  │  │ • maps_geo (地址解析)          │ │               │                           │
│  │  │ • maps_weather (天气查询)      │ │               │                           │
│  │  │ • maps_text_search (POI搜索)   │ │               │                           │
│  │  │ • maps_direction_* (路线规划)   │ │               │                           │
│  │  │ • maps_around_search (周边搜索) │ │               │                           │
│  │  │ • maps_search_detail (详情查询) │ │               │                           │
│  │  └─────────────────────────────────┘ │               │                           │
│  │                                     │               │                           │
│  │  ┌─────────────────────────────────┐ │               │                           │
│  │  │        辅助工具集                │ │               │                           │
│  │  │                               │ │               │                           │
│  │  │ • 时间工具                     │ │               │                           │
│  │  │ • 车辆控制工具                 │ │               │                           │
│  │  │ • 知识查询工具                 │ │               │                           │
│  │  │ • 内容生成工具                 │ │               │                           │
│  │  └─────────────────────────────────┘ │               │                           │
│  └─────────────────────────────────────┘               │                           │
└────────────────────────────────────────────────────────┼───────────────────────────┘
                                                         │
┌────────────────────────────────────────────────────────┼───────────────────────────┐
│                    链路二：复杂任务处理                  │                           │
├────────────────────────────────────────────────────────┼───────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      图编排引擎 (Graph Orchestrator)                        │   │
│  │                     AutoGen SelectorGroupChat                              │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      决策函数 (Decision Function)                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • 读取Redis中的AgentState                                          │    │   │
│  │  │  • 分析当前Plan执行状态                                             │    │   │
│  │  │  • 根据依赖关系选择下一个Agent                                      │    │   │
│  │  │  • 支持条件分支和并行执行                                           │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        专业Agent集群                                        │   │
│  │                                                                             │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │   │
│  │  │ 规划Agent   │  │ 研究Agent   │  │ 行程Agent   │  │ 住宿Agent   │        │   │
│  │  │Planner      │  │Researcher   │  │Itinerary    │  │Accommodation│        │   │
│  │  │Agent        │  │Agent        │  │Agent        │  │Agent        │        │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │   │
│  │                                                                             │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │   │
│  │  │ 预算Agent   │  │ 交通Agent   │  │ 整合Agent   │  │ 陪伴Agent   │        │   │
│  │  │Budget       │  │Transport    │  │Consolidator │  │Companion    │        │   │
│  │  │Agent        │  │Agent        │  │Agent        │  │Agent        │        │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │   │
│  │                                                                             │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │   │
│  │  │ 媒体Agent   │  │ 新闻Agent   │  │ 日程Agent   │  │ 用户代理     │        │   │
│  │  │Media        │  │News         │  │Scheduler    │  │User Proxy   │        │   │
│  │  │Agent        │  │Agent        │  │Agent        │  │Agent        │        │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      共享工具执行器                                          │   │
│  │                 (Shared Tool Executor)                                     │   │
│  │                                                                             │   │
│  │              所有专业Agent共享相同的工具集                                   │   │
│  │              通过Redis进行状态和结果共享                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘