#!/usr/bin/env python3
"""
统一意图分析测试示例

基于实际智谱AI模型调用的测试，验证统一分析功能的正确性和性能。
这是一个完整的测试示例，展示如何进行基于真实LLM的测试。
"""

import asyncio
import json
import time
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.core.config import get_settings
from src.core.llm_manager import LLMManager


class UnifiedIntentAnalysisTest:
    """统一意图分析测试类"""
    
    def __init__(self):
        self.settings = get_settings()
        self.llm_manager = LLMManager()
        self.test_results = []
        
    async def setup(self):
        """测试环境初始化"""
        print("🔧 初始化测试环境...")
        print(f"  - 推理模型: {self.settings.reasoning_llm.model}")
        print(f"  - API基础URL: {self.settings.reasoning_llm.base_url}")
        print(f"  - API Key: {self.settings.reasoning_llm.api_key[:10]}...")
        
    async def test_unified_analysis_simulation(self):
        """模拟统一分析功能测试"""
        print("\n" + "=" * 60)
        print("🧪 测试1: 统一意图分析功能模拟")
        print("=" * 60)
        
        # 构建统一分析的提示词
        unified_prompt = """
你是一个专业的旅行规划助手。请对用户的旅行查询进行全面分析，包括：

1. 核心意图分析 (core_intent)
2. 多城市策略分析 (multi_city_strategy) 
3. 自驾情境分析 (driving_context)
4. 偏好分析 (preference_profile)

用户查询: "{query}"

请以JSON格式返回分析结果，包含以下结构：
{{
    "core_intent": {{
        "destinations": ["目的地列表"],
        "days": 天数,
        "travel_theme": "旅行主题",
        "interests": ["兴趣点列表"],
        "confidence_score": 置信度(0-1)
    }},
    "multi_city_strategy": {{
        "strategy_type": "策略类型",
        "recommended_order": ["推荐顺序"],
        "days_allocation": {{"城市": 天数}},
        "flexibility_score": 灵活性评分(1-10)
    }},
    "driving_context": {{
        "is_driving": true/false,
        "vehicle_type": "车辆类型",
        "range_planning": {{"planning_range_km": 续航规划}}
    }},
    "preference_profile": {{
        "travel_style": "旅行风格",
        "budget_sensitivity": "预算敏感度",
        "food_importance": "美食重要性",
        "cultural_interest": "文化兴趣度"
    }}
}}
"""
        
        test_cases = [
            {
                "name": "简单单城市查询",
                "query": "我想去北京玩3天，主要想看故宫、长城这些历史文化景点",
                "expected_destinations": ["北京"],
                "expected_days": 3
            },
            {
                "name": "复杂多城市自驾查询", 
                "query": "我想开电动车去北京和上海玩5天，主要想看现代建筑和购物，预算3000元",
                "expected_destinations": ["北京", "上海"],
                "expected_days": 5
            },
            {
                "name": "度假休闲查询",
                "query": "我想去三亚度假3天，主要想海边放松和吃海鲜，预算不限",
                "expected_destinations": ["三亚"],
                "expected_days": 3
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {test_case['name']}")
            print(f"   查询: {test_case['query']}")
            
            try:
                start_time = time.time()
                
                # 调用实际的LLM进行分析
                response = await self.llm_manager.chat(
                    message=unified_prompt.format(query=test_case['query']),
                    role="reasoning"
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                content = response.get('content', '')
                token_usage = response.get('usage', {})
                
                print(f"   ✓ 分析完成，耗时: {response_time:.2f}秒")
                print(f"   📊 Token使用: {token_usage.get('total_tokens', 0)}")
                
                # 尝试解析JSON结果
                try:
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        json_content = json_match.group()
                        result = json.loads(json_content)
                        
                        # 验证结果结构
                        required_keys = ['core_intent', 'multi_city_strategy', 'driving_context', 'preference_profile']
                        missing_keys = [key for key in required_keys if key not in result]
                        
                        if not missing_keys:
                            print(f"   ✓ JSON结构完整")
                            
                            # 验证核心意图
                            core_intent = result.get('core_intent', {})
                            destinations = core_intent.get('destinations', [])
                            days = core_intent.get('days', 0)
                            
                            print(f"   📍 识别目的地: {destinations}")
                            print(f"   📅 识别天数: {days}")
                            print(f"   🎯 旅行主题: {core_intent.get('travel_theme', 'N/A')}")
                            
                            # 记录测试结果
                            self.test_results.append({
                                'test_case': test_case['name'],
                                'success': True,
                                'response_time': response_time,
                                'token_usage': token_usage.get('total_tokens', 0),
                                'destinations_correct': set(destinations) == set(test_case['expected_destinations']),
                                'days_correct': days == test_case['expected_days']
                            })
                            
                        else:
                            print(f"   ⚠️ JSON结构不完整，缺少: {missing_keys}")
                            self.test_results.append({
                                'test_case': test_case['name'],
                                'success': False,
                                'error': f"Missing keys: {missing_keys}"
                            })
                    else:
                        print(f"   ⚠️ 未找到JSON格式响应")
                        print(f"   📄 响应内容预览: {content[:200]}...")
                        self.test_results.append({
                            'test_case': test_case['name'],
                            'success': False,
                            'error': "No JSON found in response"
                        })
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    self.test_results.append({
                        'test_case': test_case['name'],
                        'success': False,
                        'error': f"JSON parse error: {e}"
                    })
                    
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                self.test_results.append({
                    'test_case': test_case['name'],
                    'success': False,
                    'error': str(e)
                })
                
    async def test_performance_comparison(self):
        """性能对比测试（模拟）"""
        print("\n" + "=" * 60)
        print("⚡ 测试2: 性能对比测试（模拟）")
        print("=" * 60)
        
        # 模拟传统4次调用的性能
        print("\n📊 模拟传统4次LLM调用:")
        
        traditional_prompts = [
            "核心意图分析: 我想去北京玩3天，主要想看故宫、长城",
            "多城市策略分析: 分析北京的旅行策略", 
            "自驾情境分析: 分析是否涉及自驾",
            "偏好分析: 分析用户的旅行偏好"
        ]
        
        total_traditional_time = 0
        total_traditional_tokens = 0
        
        for i, prompt in enumerate(traditional_prompts, 1):
            print(f"   调用 {i}/4: {prompt[:30]}...")
            
            start_time = time.time()
            response = await self.llm_manager.chat(prompt, role="basic")
            end_time = time.time()
            
            call_time = end_time - start_time
            call_tokens = response.get('usage', {}).get('total_tokens', 0)
            
            total_traditional_time += call_time
            total_traditional_tokens += call_tokens
            
            print(f"     耗时: {call_time:.2f}秒, Token: {call_tokens}")
            
        print(f"\n📈 传统模式总计:")
        print(f"   总耗时: {total_traditional_time:.2f}秒")
        print(f"   总Token: {total_traditional_tokens}")
        
        # 模拟统一调用的性能
        print("\n🚀 模拟统一LLM调用:")
        
        unified_prompt = """统一分析用户查询: 我想去北京玩3天，主要想看故宫、长城
        请同时进行核心意图、多城市策略、自驾情境和偏好分析。"""
        
        start_time = time.time()
        response = await self.llm_manager.chat(unified_prompt, role="reasoning")
        end_time = time.time()
        
        unified_time = end_time - start_time
        unified_tokens = response.get('usage', {}).get('total_tokens', 0)
        
        print(f"   耗时: {unified_time:.2f}秒")
        print(f"   Token: {unified_tokens}")
        
        # 计算性能提升
        time_improvement = (total_traditional_time - unified_time) / total_traditional_time * 100
        token_reduction = (total_traditional_tokens - unified_tokens) / total_traditional_tokens * 100
        
        print(f"\n📊 性能对比结果:")
        print(f"   ⚡ 速度提升: {time_improvement:.1f}%")
        print(f"   💰 Token节省: {token_reduction:.1f}%")
        
        if time_improvement >= 50:
            print(f"   ✅ 速度提升达标 (≥50%)")
        else:
            print(f"   ⚠️ 速度提升未达标 (<50%)")
            
        if token_reduction >= 30:
            print(f"   ✅ Token节省达标 (≥30%)")
        else:
            print(f"   ⚠️ Token节省未达标 (<30%)")
            
    async def test_edge_cases(self):
        """边界条件测试"""
        print("\n" + "=" * 60)
        print("🔍 测试3: 边界条件测试")
        print("=" * 60)
        
        edge_cases = [
            "我想去",  # 不完整查询
            "我想去火星玩10天",  # 无效目的地
            "我想去北京玩100天",  # 异常天数
            "我想去北京上海杭州苏州南京西安成都重庆玩20天",  # 过多城市
        ]
        
        for i, edge_case in enumerate(edge_cases, 1):
            print(f"\n🧪 边界测试 {i}: {edge_case}")
            
            try:
                response = await self.llm_manager.chat(
                    f"请分析这个旅行查询: {edge_case}",
                    role="basic"
                )
                
                content = response.get('content', '')
                print(f"   ✓ 系统正常处理，响应长度: {len(content)} 字符")
                print(f"   📄 响应预览: {content[:100]}...")
                
            except Exception as e:
                print(f"   ⚠️ 处理异常: {e}")
                
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        successful_tests = [r for r in self.test_results if r.get('success', False)]
        total_tests = len(self.test_results)
        success_rate = len(successful_tests) / total_tests * 100 if total_tests > 0 else 0
        
        print(f"\n📊 总体结果:")
        print(f"   测试用例总数: {total_tests}")
        print(f"   成功用例数: {len(successful_tests)}")
        print(f"   成功率: {success_rate:.1f}%")
        
        if successful_tests:
            avg_response_time = sum(r.get('response_time', 0) for r in successful_tests) / len(successful_tests)
            avg_token_usage = sum(r.get('token_usage', 0) for r in successful_tests) / len(successful_tests)
            
            print(f"\n⚡ 性能指标:")
            print(f"   平均响应时间: {avg_response_time:.2f}秒")
            print(f"   平均Token使用: {avg_token_usage:.0f}")
            
        print(f"\n📝 详细结果:")
        for result in self.test_results:
            status = "✅" if result.get('success') else "❌"
            print(f"   {status} {result['test_case']}")
            if not result.get('success'):
                print(f"      错误: {result.get('error', 'Unknown error')}")
                
        # 生成建议
        print(f"\n💡 测试建议:")
        if success_rate >= 80:
            print(f"   ✅ 测试通过率良好，可以进入下一阶段开发")
        elif success_rate >= 60:
            print(f"   ⚠️ 测试通过率一般，建议优化Prompt设计")
        else:
            print(f"   ❌ 测试通过率较低，需要重新设计统一分析方案")
            
        print(f"   📈 建议进行更多真实场景测试")
        print(f"   🔄 建议实施A/B测试对比传统模式")
        print(f"   📊 建议监控生产环境性能指标")
        
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 AutoPilot AI - 统一意图分析测试")
        print("=" * 60)
        print("基于真实智谱AI模型调用的测试验证")
        print("=" * 60)
        
        try:
            await self.setup()
            await self.test_unified_analysis_simulation()
            await self.test_performance_comparison()
            await self.test_edge_cases()
            self.generate_test_report()
            
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
        finally:
            await self.llm_manager.close_all()
            

async def main():
    """主函数"""
    test_runner = UnifiedIntentAnalysisTest()
    await test_runner.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())